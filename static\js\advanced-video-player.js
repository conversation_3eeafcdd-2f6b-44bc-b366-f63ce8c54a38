/**
 * مشغل الفيديو المتقدم باستخدام YouTube IFrame API
 * Advanced Video Player using YouTube IFrame API
 */

class AdvancedVideoPlayer {
    constructor(containerId, videoId, options = {}) {
        this.containerId = containerId;
        this.videoId = videoId;
        this.player = null;
        this.isReady = false;
        this.isPlaying = false;
        this.currentTime = 0;
        this.duration = 0;
        this.volume = 100;
        this.isMuted = false;
        this.isFullscreen = false;
        
        // خيارات المشغل
        this.options = {
            width: '100%',
            height: '100%',
            autoplay: false,
            showControls: false,
            enableKeyboard: true,
            ...options
        };
        
        // معرفات العناصر
        this.playerId = `youtube-player-${Date.now()}`;
        this.controlsId = `video-controls-${Date.now()}`;
        
        // متغيرات التتبع
        this.progressUpdateInterval = null;
        this.hideControlsTimeout = null;
        this.lastInteraction = Date.now();
        this.lastPercentage = 0;
        
        // أحداث مخصصة
        this.events = {
            onReady: options.onReady || (() => {}),
            onStateChange: options.onStateChange || (() => {}),
            onProgress: options.onProgress || (() => {}),
            onTimeUpdate: options.onTimeUpdate || (() => {})
        };
        
        this.init();
    }
    
    init() {
        // إنشاء هيكل المشغل
        this.createPlayerStructure();

        // تحميل YouTube IFrame API إذا لم يكن محملاً
        this.loadYouTubeAPI();

        // إعداد أحداث الشاشة الكاملة
        this.setupFullscreenEvents();

        // إعداد أحداث اللمس للموبايل
        this.setupTouchEvents();

        // تحميل إعدادات المستخدم المحفوظة
        this.loadUserPreferences();
    }
    
    createPlayerStructure() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`Container with ID ${this.containerId} not found`);
            return;
        }
        
        container.innerHTML = `
            <div class="advanced-video-player" data-video-id="${this.videoId}">
                <div class="video-wrapper">
                    <div id="${this.playerId}" class="youtube-player"></div>
                    <div class="video-overlay" id="videoOverlay">
                        <div class="play-button-overlay" id="playButtonOverlay">
                            <button class="play-btn-large" id="playBtnLarge">
                                <i class="fas fa-play"></i>
                            </button>
                        </div>
                        <div class="loading-spinner" id="loadingSpinner">
                            <div class="spinner"></div>
                        </div>
                    </div>
                </div>
                
                <div class="video-controls" id="${this.controlsId}">
                    <div class="controls-row">
                        <!-- شريط التقدم -->
                        <div class="progress-container">
                            <div class="progress-bar" id="progressBar">
                                <div class="progress-buffer" id="progressBuffer"></div>
                                <div class="progress-played" id="progressPlayed"></div>
                                <div class="progress-handle" id="progressHandle"></div>
                            </div>
                        </div>
                        
                        <!-- أزرار التحكم -->
                        <div class="controls-buttons">
                            <div class="controls-left">
                                <button class="control-btn play-pause-btn" id="playPauseBtn">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="control-btn" id="rewindBtn" title="الرجوع 10 ثوان">
                                    <i class="fas fa-undo"></i>
                                    <span class="btn-text">10</span>
                                </button>
                                <button class="control-btn" id="forwardBtn" title="التقدم 10 ثوان">
                                    <i class="fas fa-redo"></i>
                                    <span class="btn-text">10</span>
                                </button>
                                <div class="volume-control">
                                    <button class="control-btn volume-btn" id="volumeBtn">
                                        <i class="fas fa-volume-up"></i>
                                    </button>
                                    <div class="volume-slider" id="volumeSlider">
                                        <input type="range" min="0" max="100" value="100" id="volumeRange">
                                    </div>
                                </div>
                                <div class="time-display" id="timeDisplay">
                                    <span id="currentTimeDisplay">0:00</span>
                                    <span class="time-separator">/</span>
                                    <span id="durationDisplay">0:00</span>
                                </div>
                            </div>
                            
                            <div class="controls-right">
                                <button class="control-btn" id="settingsBtn" title="الإعدادات">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <button class="control-btn" id="fullscreenBtn" title="شاشة كاملة">
                                    <i class="fas fa-expand"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- قائمة الإعدادات -->
                <div class="settings-menu" id="settingsMenu">
                    <div class="settings-item">
                        <span>جودة الفيديو</span>
                        <select id="qualitySelect">
                            <option value="auto">تلقائي</option>
                        </select>
                    </div>
                    <div class="settings-item">
                        <span>سرعة التشغيل</span>
                        <select id="speedSelect">
                            <option value="0.25">0.25x</option>
                            <option value="0.5">0.5x</option>
                            <option value="0.75">0.75x</option>
                            <option value="1" selected>1x</option>
                            <option value="1.25">1.25x</option>
                            <option value="1.5">1.5x</option>
                            <option value="2">2x</option>
                        </select>
                    </div>
                </div>
            </div>
        `;
        
        // إعداد أحداث التحكم
        this.setupControlEvents();

        // منع القائمة السياقية
        this.disableContextMenu();
    }
    
    loadYouTubeAPI() {
        // التحقق من وجود YouTube API
        if (window.YT && window.YT.Player) {
            this.initializePlayer();
            return;
        }
        
        // تحميل YouTube IFrame API
        if (!window.onYouTubeIframeAPIReady) {
            const tag = document.createElement('script');
            tag.src = 'https://www.youtube.com/iframe_api';
            const firstScriptTag = document.getElementsByTagName('script')[0];
            firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
            
            // إعداد callback عندما يكون API جاهزاً
            window.onYouTubeIframeAPIReady = () => {
                this.initializePlayer();
            };
        } else {
            // API قيد التحميل، انتظار
            const checkAPI = setInterval(() => {
                if (window.YT && window.YT.Player) {
                    clearInterval(checkAPI);
                    this.initializePlayer();
                }
            }, 100);
        }
    }
    
    initializePlayer() {
        this.player = new YT.Player(this.playerId, {
            height: '100%',
            width: '100%',
            videoId: this.videoId,
            playerVars: {
                'autoplay': this.options.autoplay ? 1 : 0,
                'controls': 0,           // إخفاء عناصر التحكم
                'modestbranding': 1,     // إخفاء شعار YouTube
                'rel': 0,                // عدم عرض فيديوهات مقترحة
                'showinfo': 0,           // إخفاء معلومات الفيديو
                'iv_load_policy': 3,     // إخفاء التعليقات التوضيحية
                'disablekb': 1,          // تعطيل لوحة المفاتيح الافتراضية
                'fs': 0,                 // تعطيل زر الشاشة الكاملة
                'playsinline': 1,        // تشغيل مضمن في الموبايل
                'cc_load_policy': 0,     // إخفاء الترجمة
                'enablejsapi': 1,        // تفعيل JavaScript API
                'origin': window.location.origin
            },
            events: {
                'onReady': (event) => this.onPlayerReady(event),
                'onStateChange': (event) => this.onPlayerStateChange(event),
                'onError': (event) => this.onPlayerError(event)
            }
        });
    }
    
    onPlayerReady(event) {
        this.isReady = true;
        this.duration = this.player.getDuration();
        this.updateDurationDisplay();
        this.updateQualityOptions();
        
        // إخفاء spinner التحميل
        document.getElementById('loadingSpinner').style.display = 'none';
        
        // إظهار زر التشغيل الكبير
        document.getElementById('playButtonOverlay').style.display = 'flex';
        
        // بدء تحديث التقدم
        this.startProgressUpdate();
        
        // استدعاء callback المخصص
        this.events.onReady(event);
        
        console.log('YouTube Player Ready');
    }
    
    onPlayerStateChange(event) {
        const state = event.data;
        
        switch (state) {
            case YT.PlayerState.PLAYING:
                this.isPlaying = true;
                this.updatePlayPauseButton();
                document.getElementById('playButtonOverlay').style.display = 'none';
                break;
                
            case YT.PlayerState.PAUSED:
                this.isPlaying = false;
                this.updatePlayPauseButton();
                break;
                
            case YT.PlayerState.ENDED:
                this.isPlaying = false;
                this.updatePlayPauseButton();
                document.getElementById('playButtonOverlay').style.display = 'flex';
                this.events.onProgress(100); // إشارة إكمال الفيديو
                break;
                
            case YT.PlayerState.BUFFERING:
                document.getElementById('loadingSpinner').style.display = 'block';
                break;
                
            default:
                document.getElementById('loadingSpinner').style.display = 'none';
                break;
        }
        
        // استدعاء callback المخصص
        this.events.onStateChange(event);
    }
    
    onPlayerError(event) {
        console.error('YouTube Player Error:', event.data);
        
        // إظهار رسالة خطأ للمستخدم
        const errorMessages = {
            2: 'معرف الفيديو غير صحيح',
            5: 'خطأ في تشغيل الفيديو',
            100: 'الفيديو غير موجود أو محذوف',
            101: 'الفيديو غير متاح في منطقتك',
            150: 'الفيديو غير متاح في منطقتك'
        };
        
        const errorMessage = errorMessages[event.data] || 'حدث خطأ في تشغيل الفيديو';
        this.showError(errorMessage);
    }
    
    setupControlEvents() {
        // زر التشغيل/الإيقاف الكبير
        document.getElementById('playBtnLarge').addEventListener('click', () => {
            this.togglePlayPause();
        });

        // زر التشغيل/الإيقاف في شريط التحكم
        document.getElementById('playPauseBtn').addEventListener('click', () => {
            this.togglePlayPause();
        });

        // أزرار الترجيع والتقدم
        document.getElementById('rewindBtn').addEventListener('click', () => {
            this.seekBy(-10);
        });

        document.getElementById('forwardBtn').addEventListener('click', () => {
            this.seekBy(10);
        });

        // التحكم في الصوت
        document.getElementById('volumeBtn').addEventListener('click', () => {
            this.toggleMute();
        });

        document.getElementById('volumeRange').addEventListener('input', (e) => {
            this.setVolume(parseInt(e.target.value));
        });

        // شريط التقدم
        this.setupProgressBar();

        // الشاشة الكاملة
        document.getElementById('fullscreenBtn').addEventListener('click', () => {
            this.toggleFullscreen();
        });

        // الإعدادات
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.toggleSettings();
        });

        // تغيير الجودة والسرعة
        document.getElementById('qualitySelect').addEventListener('change', (e) => {
            this.setQuality(e.target.value);
        });

        document.getElementById('speedSelect').addEventListener('change', (e) => {
            this.setPlaybackRate(parseFloat(e.target.value));
        });

        // أحداث لوحة المفاتيح
        if (this.options.enableKeyboard) {
            this.setupKeyboardEvents();
        }

        // إخفاء/إظهار عناصر التحكم
        this.setupControlsVisibility();
    }

    setupProgressBar() {
        const progressBar = document.getElementById('progressBar');
        const progressHandle = document.getElementById('progressHandle');
        let isDragging = false;

        const startDrag = (e) => {
            isDragging = true;
            document.addEventListener('mousemove', onDrag);
            document.addEventListener('mouseup', endDrag);
            document.addEventListener('touchmove', onDrag);
            document.addEventListener('touchend', endDrag);
        };

        const onDrag = (e) => {
            if (!isDragging || !this.isReady) return;

            const rect = progressBar.getBoundingClientRect();
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width));
            const newTime = percentage * this.duration;

            this.updateProgressDisplay(percentage);
            this.currentTime = newTime;
        };

        const endDrag = () => {
            if (isDragging) {
                isDragging = false;
                this.seekTo(this.currentTime);
                document.removeEventListener('mousemove', onDrag);
                document.removeEventListener('mouseup', endDrag);
                document.removeEventListener('touchmove', onDrag);
                document.removeEventListener('touchend', endDrag);
            }
        };

        progressBar.addEventListener('mousedown', startDrag);
        progressBar.addEventListener('touchstart', startDrag);
        progressHandle.addEventListener('mousedown', startDrag);
        progressHandle.addEventListener('touchstart', startDrag);

        // النقر على شريط التقدم للانتقال
        progressBar.addEventListener('click', (e) => {
            if (!this.isReady) return;

            const rect = progressBar.getBoundingClientRect();
            const percentage = (e.clientX - rect.left) / rect.width;
            const newTime = percentage * this.duration;
            this.seekTo(newTime);
        });
    }

    setupKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            // التأكد من أن المشغل نشط
            if (!this.isPlayerFocused()) return;

            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.seekBy(-5);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.seekBy(5);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.setVolume(Math.min(100, this.volume + 10));
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.setVolume(Math.max(0, this.volume - 10));
                    break;
                case 'KeyM':
                    e.preventDefault();
                    this.toggleMute();
                    break;
                case 'KeyF':
                    e.preventDefault();
                    this.toggleFullscreen();
                    break;
            }
        });
    }

    setupControlsVisibility() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const controls = document.getElementById(this.controlsId);

        const showControls = () => {
            controls.classList.add('visible');
            this.lastInteraction = Date.now();

            // إخفاء عناصر التحكم بعد 3 ثوان من عدم التفاعل
            clearTimeout(this.hideControlsTimeout);
            this.hideControlsTimeout = setTimeout(() => {
                if (this.isPlaying && Date.now() - this.lastInteraction > 3000) {
                    controls.classList.remove('visible');
                }
            }, 3000);
        };

        const hideControls = () => {
            if (!this.isPlaying) return;
            controls.classList.remove('visible');
        };

        // إظهار عناصر التحكم عند الحركة أو اللمس
        playerContainer.addEventListener('mousemove', showControls);
        playerContainer.addEventListener('touchstart', showControls);
        playerContainer.addEventListener('click', showControls);

        // إخفاء عناصر التحكم عند مغادرة المشغل
        playerContainer.addEventListener('mouseleave', hideControls);

        // إظهار عناصر التحكم افتراضياً
        showControls();
    }

    // وظائف التحكم الأساسية
    togglePlayPause() {
        if (!this.isReady) return;

        if (this.isPlaying) {
            this.player.pauseVideo();
        } else {
            this.player.playVideo();
        }
    }

    seekTo(seconds) {
        if (!this.isReady) return;
        this.player.seekTo(seconds, true);
    }

    seekBy(seconds) {
        if (!this.isReady) return;
        const newTime = Math.max(0, Math.min(this.duration, this.currentTime + seconds));
        this.seekTo(newTime);
    }

    setVolume(volume) {
        if (!this.isReady) return;

        this.volume = Math.max(0, Math.min(100, volume));
        this.player.setVolume(this.volume);

        // تحديث واجهة التحكم في الصوت
        document.getElementById('volumeRange').value = this.volume;
        this.updateVolumeButton();

        // إلغاء كتم الصوت إذا كان مكتوماً
        if (this.isMuted && this.volume > 0) {
            this.isMuted = false;
            this.updateVolumeButton();
        }
    }

    toggleMute() {
        if (!this.isReady) return;

        if (this.isMuted) {
            this.player.unMute();
            this.isMuted = false;
        } else {
            this.player.mute();
            this.isMuted = true;
        }

        this.updateVolumeButton();
    }

    setPlaybackRate(rate) {
        if (!this.isReady) return;
        this.player.setPlaybackRate(rate);
    }

    setQuality(quality) {
        if (!this.isReady) return;

        if (quality === 'auto') {
            // YouTube سيختار الجودة تلقائياً
            return;
        }

        this.player.setPlaybackQuality(quality);
    }

    toggleFullscreen() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);

        if (!this.isFullscreen) {
            if (playerContainer.requestFullscreen) {
                playerContainer.requestFullscreen();
            } else if (playerContainer.webkitRequestFullscreen) {
                playerContainer.webkitRequestFullscreen();
            } else if (playerContainer.msRequestFullscreen) {
                playerContainer.msRequestFullscreen();
            }
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.webkitExitFullscreen) {
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                document.msExitFullscreen();
            }
        }
    }

    toggleSettings() {
        const settingsMenu = document.getElementById('settingsMenu');
        settingsMenu.classList.toggle('visible');
    }

    // وظائف التحديث
    startProgressUpdate() {
        // استخدام الوظيفة المحسنة للأداء
        this.optimizePerformance();
    }

    updateProgressDisplay(percentage) {
        const progressPlayed = document.getElementById('progressPlayed');
        const progressHandle = document.getElementById('progressHandle');

        progressPlayed.style.width = (percentage * 100) + '%';
        progressHandle.style.left = (percentage * 100) + '%';
    }

    updateTimeDisplay() {
        document.getElementById('currentTimeDisplay').textContent = this.formatTime(this.currentTime);
        document.getElementById('durationDisplay').textContent = this.formatTime(this.duration);
    }

    updateDurationDisplay() {
        document.getElementById('durationDisplay').textContent = this.formatTime(this.duration);
    }

    updatePlayPauseButton() {
        const playPauseBtn = document.getElementById('playPauseBtn');
        const playBtnLarge = document.getElementById('playBtnLarge');

        if (this.isPlaying) {
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            playBtnLarge.innerHTML = '<i class="fas fa-pause"></i>';
        } else {
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            playBtnLarge.innerHTML = '<i class="fas fa-play"></i>';
        }
    }

    updateVolumeButton() {
        const volumeBtn = document.getElementById('volumeBtn');

        if (this.isMuted || this.volume === 0) {
            volumeBtn.innerHTML = '<i class="fas fa-volume-mute"></i>';
        } else if (this.volume < 50) {
            volumeBtn.innerHTML = '<i class="fas fa-volume-down"></i>';
        } else {
            volumeBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        }
    }

    updateQualityOptions() {
        const qualitySelect = document.getElementById('qualitySelect');
        const availableQualities = this.player.getAvailableQualityLevels();

        // مسح الخيارات الموجودة
        qualitySelect.innerHTML = '<option value="auto">تلقائي</option>';

        // إضافة الجودات المتاحة
        const qualityLabels = {
            'hd2160': '4K (2160p)',
            'hd1440': '1440p',
            'hd1080': '1080p',
            'hd720': '720p',
            'large': '480p',
            'medium': '360p',
            'small': '240p',
            'tiny': '144p'
        };

        availableQualities.forEach(quality => {
            const option = document.createElement('option');
            option.value = quality;
            option.textContent = qualityLabels[quality] || quality;
            qualitySelect.appendChild(option);
        });
    }

    // وظائف مساعدة
    formatTime(seconds) {
        if (!seconds || isNaN(seconds)) return '0:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    isPlayerFocused() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        return playerContainer && playerContainer.contains(document.activeElement);
    }

    showError(message) {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        const errorDiv = document.createElement('div');
        errorDiv.className = 'video-error';
        errorDiv.innerHTML = `
            <div class="error-content">
                <i class="fas fa-exclamation-triangle"></i>
                <p>${message}</p>
            </div>
        `;
        playerContainer.appendChild(errorDiv);
    }

    // وظائف التحكم العامة
    play() {
        if (this.isReady) this.player.playVideo();
    }

    pause() {
        if (this.isReady) this.player.pauseVideo();
    }

    stop() {
        if (this.isReady) this.player.stopVideo();
    }

    getCurrentTime() {
        return this.isReady ? this.player.getCurrentTime() : 0;
    }

    getDuration() {
        return this.isReady ? this.player.getDuration() : 0;
    }

    getVolume() {
        return this.volume;
    }

    disableContextMenu() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);

        // منع القائمة السياقية
        playerContainer.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            return false;
        });

        // منع السحب والإفلات
        playerContainer.addEventListener('dragstart', (e) => {
            e.preventDefault();
            return false;
        });

        // منع التحديد
        playerContainer.addEventListener('selectstart', (e) => {
            e.preventDefault();
            return false;
        });
    }

    // أحداث الشاشة الكاملة
    setupFullscreenEvents() {
        const updateFullscreenButton = () => {
            const fullscreenBtn = document.getElementById('fullscreenBtn');

            if (document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement) {
                this.isFullscreen = true;
                fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                fullscreenBtn.title = 'إنهاء الشاشة الكاملة';
            } else {
                this.isFullscreen = false;
                fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                fullscreenBtn.title = 'شاشة كاملة';
            }
        };

        document.addEventListener('fullscreenchange', updateFullscreenButton);
        document.addEventListener('webkitfullscreenchange', updateFullscreenButton);
        document.addEventListener('msfullscreenchange', updateFullscreenButton);
    }

    // تحديث buffer التحميل
    updateBufferProgress() {
        if (!this.isReady) return;

        try {
            const buffered = this.player.getVideoLoadedFraction();
            const progressBuffer = document.getElementById('progressBuffer');
            progressBuffer.style.width = (buffered * 100) + '%';
        } catch (error) {
            // تجاهل الأخطاء في حالة عدم توفر البيانات
        }
    }

    // إعداد أحداث اللمس للموبايل
    setupTouchEvents() {
        const playerContainer = document.querySelector(`#${this.containerId} .advanced-video-player`);
        let touchStartTime = 0;
        let touchStartX = 0;
        let touchStartY = 0;

        playerContainer.addEventListener('touchstart', (e) => {
            touchStartTime = Date.now();
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
        });

        playerContainer.addEventListener('touchend', (e) => {
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - touchStartTime;
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;

            const deltaX = Math.abs(touchEndX - touchStartX);
            const deltaY = Math.abs(touchEndY - touchStartY);

            // إذا كان لمسة سريعة وليس سحب
            if (touchDuration < 300 && deltaX < 10 && deltaY < 10) {
                // تبديل عرض عناصر التحكم
                const controls = document.getElementById(this.controlsId);
                if (controls.classList.contains('visible')) {
                    controls.classList.remove('visible');
                } else {
                    controls.classList.add('visible');
                    this.lastInteraction = Date.now();
                }
            }
        });
    }

    // تحسين الأداء
    optimizePerformance() {
        // تقليل تكرار تحديث التقدم عند عدم التشغيل
        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
        }

        this.progressUpdateInterval = setInterval(() => {
            if (this.isReady && this.player) {
                this.currentTime = this.player.getCurrentTime();
                this.duration = this.player.getDuration();

                const percentage = this.duration > 0 ? (this.currentTime / this.duration) : 0;

                // تحديث العرض فقط إذا كان هناك تغيير ملحوظ
                if (Math.abs(percentage - this.lastPercentage) > 0.001) {
                    this.updateProgressDisplay(percentage);
                    this.updateTimeDisplay();
                    this.updateBufferProgress();
                    this.lastPercentage = percentage;
                }

                // استدعاء callback التحديث
                this.events.onTimeUpdate({
                    currentTime: this.currentTime,
                    duration: this.duration,
                    percentage: percentage * 100
                });
            }
        }, this.isPlaying ? 500 : 2000); // تحديث أسرع أثناء التشغيل
    }

    // إعادة تهيئة المشغل
    reinitialize(newVideoId) {
        if (this.player && this.isReady) {
            this.videoId = newVideoId;
            this.player.loadVideoById(newVideoId);
            this.currentTime = 0;
            this.updateProgressDisplay(0);
            this.updateTimeDisplay();
        }
    }

    // الحصول على معلومات الفيديو
    getVideoInfo() {
        if (!this.isReady) return null;

        try {
            return {
                title: this.player.getVideoData().title,
                author: this.player.getVideoData().author,
                duration: this.duration,
                currentTime: this.currentTime,
                quality: this.player.getPlaybackQuality(),
                availableQualities: this.player.getAvailableQualityLevels(),
                playbackRate: this.player.getPlaybackRate(),
                volume: this.volume,
                isMuted: this.isMuted,
                isPlaying: this.isPlaying
            };
        } catch (error) {
            console.error('Error getting video info:', error);
            return null;
        }
    }

    // حفظ إعدادات المستخدم
    saveUserPreferences() {
        const preferences = {
            volume: this.volume,
            playbackRate: this.player ? this.player.getPlaybackRate() : 1,
            quality: this.player ? this.player.getPlaybackQuality() : 'auto'
        };

        localStorage.setItem('videoPlayerPreferences', JSON.stringify(preferences));
    }

    // تحميل إعدادات المستخدم
    loadUserPreferences() {
        try {
            const preferences = JSON.parse(localStorage.getItem('videoPlayerPreferences'));
            if (preferences) {
                if (preferences.volume !== undefined) {
                    this.setVolume(preferences.volume);
                }
                if (preferences.playbackRate !== undefined) {
                    this.setPlaybackRate(preferences.playbackRate);
                    document.getElementById('speedSelect').value = preferences.playbackRate;
                }
                if (preferences.quality !== undefined && preferences.quality !== 'auto') {
                    this.setQuality(preferences.quality);
                    document.getElementById('qualitySelect').value = preferences.quality;
                }
            }
        } catch (error) {
            console.error('Error loading user preferences:', error);
        }
    }

    destroy() {
        // حفظ إعدادات المستخدم
        this.saveUserPreferences();

        if (this.progressUpdateInterval) {
            clearInterval(this.progressUpdateInterval);
        }

        if (this.hideControlsTimeout) {
            clearTimeout(this.hideControlsTimeout);
        }

        if (this.player) {
            this.player.destroy();
        }

        // إزالة أحداث الشاشة الكاملة
        document.removeEventListener('fullscreenchange', this.updateFullscreenButton);
        document.removeEventListener('webkitfullscreenchange', this.updateFullscreenButton);
        document.removeEventListener('msfullscreenchange', this.updateFullscreenButton);
    }
}

// دالة مساعدة لإنشاء مشغل فيديو جديد
function createAdvancedVideoPlayer(containerId, videoId, options = {}) {
    return new AdvancedVideoPlayer(containerId, videoId, options);
}

// تصدير الكلاس والدالة للاستخدام العام
window.AdvancedVideoPlayer = AdvancedVideoPlayer;
window.createAdvancedVideoPlayer = createAdvancedVideoPlayer;
